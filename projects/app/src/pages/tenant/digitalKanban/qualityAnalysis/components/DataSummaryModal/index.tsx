import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { Button, Select, Table, Segmented } from 'antd';
import {
  getSubjectSummarizationTable,
  exportSubjectSummarizationTable,
  getSubjectSummarizationGraph
} from '@/api/kanban';
import MyModal from '@/components/MyModal';
import { Box, ModalBody, Flex } from '@chakra-ui/react';
import BoardPane, { BoardPaneRef } from '@/components/BoardPane';
import { GetSubjectSummarizationTableType } from '@/types/api/kanban';
import { useUserStore } from '@/store/useUserStore';
import NoDataComponent from '../NoDataProps';
import EChartsReact from 'echarts-for-react';
import Lottie from '@/components/Lottie';
import { respDims } from '@/utils/chakra';
import styles from './index.module.scss';
import html2canvas from 'html2canvas';
import { uploadFile } from '@/api/file';

export interface DataSummaryModalRef {
  getTableImageUrl: () => string;
  generateTableImage: (params?: {
    year: string;
    term: number;
    startDate: string;
    endDate: string;
    selectedSubjects: string[];
    selectedGradesName: string;
    selectedGradesId: string;
    selectedTestData: string[];
  }) => Promise<string>;
  getBoardPaneContent: (params?: {
    year: string;
    term: number;
    startDate: string;
    endDate: string;
    selectedSubjects: string[];
    selectedGradesName: string;
    selectedGradesId: string;
    selectedTestData: string[];
  }) => Promise<string>;
  refreshBoardPane: () => void;
}

interface DataSummaryModalProps {
  isModalVisible: boolean;
  setIsModalVisible: (visible: boolean) => void;
  subjectOptions: any;
  selectedYear: string;
  selectedTerm: number;
  selectedSubjects: string[];
  selectedGradesName: string;
  selectedGradesId: string;
  selectedTestData: string[];
  startDate: string;
  endDate: string;
  chartOptions: any;
  isAuthority: boolean;
  onClose: () => void;
  onImageUrlChange?: (imageUrl: string) => void;
}

const DataSummaryModal = forwardRef<DataSummaryModalRef, DataSummaryModalProps>(({
  isModalVisible,
  isAuthority,
  setIsModalVisible,
  subjectOptions,
  selectedYear,
  selectedTerm,
  selectedSubjects,
  selectedGradesName,
  selectedGradesId,
  selectedTestData,
  startDate,
  endDate,
  chartOptions,
  onClose,
  onImageUrlChange
}, ref) => {
  const [data, setData] = useState<GetSubjectSummarizationTableType[]>([]);
  const allSubjects = subjectOptions?.map((option: any) => option.value);
  const [selectedSubjectsState, setSelectedSubjectsState] = useState<string[]>([]);
  const { teacherType } = useUserStore();
  const echartsRef = useRef<any | null>(null);
  const [examSummary, setExamSummary] = useState<any>({});
  const [viewMode, setViewMode] = useState<'table' | 'chart'>('table');
  const [chartModalOptions, setChartModalOptions] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [chartHeight, setChartHeight] = useState<string>('');
  const [tableImageUrl, setTableImageUrl] = useState<string>('');
  const tableRef = useRef<HTMLDivElement>(null);
  const boardPaneRef = useRef<BoardPaneRef>(null);
  const [boardPaneContent, setBoardPaneContent] = useState<string>('');

  const handleCancel = () => {
    setIsModalVisible(false);
    onClose();
  };

  const handleCopyUrl = () => {
    if (tableImageUrl) {
      navigator.clipboard.writeText(tableImageUrl)
        .then(() => {
          console.log('图片URL已复制到剪贴板');
          // 可以添加一个提示，如使用message.success('图片URL已复制到剪贴板')
        })
        .catch(err => {
          console.error('复制失败:', err);
        });
    }
  };

  const handleExport = async () => {
    if (viewMode === 'chart') {
      const chartInstance = echartsRef.current?.getEchartsInstance();
      if (!chartInstance) return;

      // 设置 dataZoom 以显示完整图表
      chartInstance.setOption({
        xAxis: {
          axisLabel: {
            rotate: 30 // 增加旋转角度以减少重叠
          }
        },
        series: [
          {
            barWidth: 8 // 设置柱子的宽度为5
          }
        ]
      });

      // 显示加载指示器
      chartInstance.showLoading('default', {
        text: '导出中...',
        color: '#175DFF',
        textColor: '#000',
        maskColor: '#fff',
        zlevel: 0
      });

      const setChartOptions = (start: number, end: number, rotate: number, barWidth: number) => {
        chartInstance.setOption({
          xAxis: { axisLabel: { rotate } },
          series: [{ barWidth }]
        });
      };

      // 等待图表更新完成后再导出
      setTimeout(() => {
        chartInstance.hideLoading(); // 确保在导出前隐藏加载指示器
        const imgData = chartInstance.getDataURL({
          type: 'png',
          pixelRatio: 2,
          backgroundColor: '#fff'
        });

        const link = document.createElement('a');
        link.href = imgData;
        link.download = 'chart.png';
        link.click();

        setChartOptions(0, 30, 0, 8);
      }, 500);
    } else {
      try {
        const response = await exportSubjectSummarizationTable({
          year: selectedYear,
          term: selectedTerm,
          startDate: startDate,
          endDate: endDate,
          gradeName: selectedGradesName,
          gradeId: selectedGradesId,
          subjectNames: selectedSubjectsState.length ? selectedSubjectsState : selectedSubjects,
          examType: selectedTestData?.length ? selectedTestData[0] : '',
          examId: selectedTestData?.[1] || '',
          teacherType: teacherType
        });

        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${selectedGradesName}各班数据汇总统计表.xlsx`);
        document.body.appendChild(link);
        link.click();
        link?.parentNode?.removeChild(link);
      } catch (error) {
        console.error('导出失败:', error);
      }
    }
  };

  const generateExamSummary = (params: any, responseData: any[]) => {
    const result = {
      academic_year: params.year,
      semester: params.term === 1 ? '第一学期' : '第二学期',
      grade: params.gradeName,
      test_type: params.examType,
      test_name: '期末质量分析',
      subject: Array.isArray(params.subjectNames)
        ? params.subjectNames
        : (params.subjectNames || '').split(','),
      data_block_name: '数据汇总',
      data_render_type: '表格',
      data: [] as any[],
      analysis_requirements: '请你对本次测试的班级超均率进行分析'
    };

    responseData.forEach((item) => {
      const className = item.className ? item.className.substring(0, 3) : '';
      const exceedingAverageRate = parseFloat(item.overAverage) / 100;

      result.data.push({
        class_name: className,
        subject: item.subjectName,
        exceeding_average_rate: exceedingAverageRate
      });
    });

    return result;
  };

  const getSubjectColor = (subject: string) => {
    const colors = [
      '#175DFF',
      '#14C9C9',
      '#34A7FA',
      '#F7BA1E',
      '#FF6678',
      '#722ED1',
      '#A0D7FF',
      '#FF5733',
      '#33FF57',
      '#3357FF',
      '#FF33A1',
      '#A133FF',
      '#FF8C33',
      '#FF6347',
      '#4682B4' // 替换掉不喜欢的颜色
    ];

    // 使用学科名称的哈希值来选择颜色
    const hash = Array.from(subject).reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const colorIndex = hash % colors.length;

    switch (subject) {
      case '语文':
        return colors[0];
      case '数学':
        return colors[1];
      case '英语':
        return colors[2];
      case '道法':
        return colors[3];
      case '历史':
        return colors[4];
      case '物理':
        return colors[5];
      case '化学':
        return colors[6];
      default:
      // 如果没有匹配的学科名称，使用 echarts 随机颜色
      // return colors[colorIndex];
    }
  };

  // 处理数据，生成图表配置
  const processChartData = (data: any[]) => {
    // 提取唯一的班级名称
    const classNames = Array.from(new Set(data.map((item) => item.className)));

    // 提取唯一的学科名称
    const subjectNames = Array.from(new Set(data.map((item) => item.subjectName)));

    // 按学科分组，生成 series 数据
    const series = subjectNames
      .map((subject) => {
        const subjectData = data
          .filter((item) => item.subjectName === subject)
          .map((item) => parseFloat(item.overAverage)); // 将 overAverage 转换为数值

        // 仅在 subjectData 不为空时创建 series 条目
        if (subjectData.length > 0) {
          return {
            name: subject,
            type: 'bar',
            data: subjectData,
            itemStyle: {
              color: getSubjectColor(subject) // 根据学科设置颜色
            },
            label: {
              show: false
            },
            barWidth: 'auto', // 设置为 'auto' 以实现自适应宽度
            barGap: '0%' // 减少组间的间隔
          };
        }
        return null;
      })
      .filter(Boolean); // 过滤掉 null 值

    // 返回图表配置
    return {
      title: {
        text: '', //数据汇总
        left: 'left'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params: any) {
          let result = params[0].name + '<br/>';
          params.forEach(function (item: any) {
            result += item.marker + ' ' + item.seriesName + ': ' + item.data + '%' + '<br/>';
          });
          return result;
        },
        textStyle: {
          align: 'left'
        }
      },
      legend: {
        data: subjectNames,
        bottom: '3%',
        icon: 'circle'
      },
      grid: {
        top: '12%',
        left: '0%',
        right: '0%',
        // bottom: '20%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: classNames,
        axisLabel: {
          interval: 0,
          rotate: 0, // 增加旋转角度以减少重叠
          formatter: function (value: string) {
            return value.length > 5 ? value.slice(0, 5) + '...' : value; // 缩短标签长度
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value} %'
        }
      },
      // dataZoom: [
      //   {
      //     show: true,
      //     type: 'slider',
      //     realtime: true,
      //     start: 0,
      //     end: 30,
      //     height: 12,
      //     bottom: 0,
      //     handleSize: 0,
      //     borderColor: '#fff',
      //     showDetail: false,
      //     fillerColor: '#eee',
      //     backgroundColor: '#fff',
      //     showDataShadow: false,
      //     minSpan: 30,
      //     zoomLock: true
      //   },
      //   {
      //     type: 'inside',
      //     realtime: true,
      //     start: 0,
      //     end: 30,
      //     minSpan: 30,
      //     zoomLock: true
      //   }
      // ],
      series: series
    };
  };

  useEffect(() => {
    if (isModalVisible) {
      const parasms = {
        year: selectedYear,
        term: selectedTerm,
        startDate: startDate,
        endDate: endDate,
        subjectNames: selectedSubjectsState.length ? selectedSubjectsState : selectedSubjects,
        gradeName: selectedGradesName,
        gradeId: selectedGradesId,
        examType: selectedTestData?.[0] || '',
        teacherType: teacherType,
        examId: selectedTestData?.[1] || ''
      };

      setIsLoading(true);

      getSubjectSummarizationTable(parasms)
        .then((response) => {
          setData(response);
          const res = generateExamSummary(parasms, response);
          setExamSummary(res);

          // 模态框打开后，延迟获取BoardPane的内容
          setTimeout(() => {
            if (boardPaneRef.current) {
              boardPaneRef.current.refresh();
            }
          }, 1000);
        })
        .catch((error) => {
          console.error('Error fetching data:', error);
        });

      // 调用 getSubjectSummarizationGraph 更新图表数据
      getSubjectSummarizationGraph(parasms)
        .then((graphData) => {
          const res = processChartData(graphData);
          const chartHeightValue = Math.max(Math.min(graphData.length * 12, 1600), 1100) + 'px'; // 限制 chartHeight 最大为 1600px，最小为 900px
          setChartHeight(chartHeightValue);
          setChartModalOptions({});

          setTimeout(() => {
            setChartModalOptions(res);
          }, 500);
        })
        .catch((error) => {
          console.error('Error fetching graph data:', error);
        });

      setTimeout(() => {
        setIsLoading(false);
      }, 500);
    }
  }, [isModalVisible, selectedSubjectsState]);

  useEffect(() => {
    // 检查 "全部" 是否在第一项，并且后面有新增选项
    if (selectedSubjectsState[0] === '' && selectedSubjectsState.length > 1) {
      setSelectedSubjectsState(selectedSubjectsState.slice(1));
    }

    // 检查 "全部" 是否在最后一项，并且前面有其他选项
    if (
      selectedSubjectsState[selectedSubjectsState.length - 1] === '' &&
      selectedSubjectsState.length > 1
    ) {
      // 只保留 "全部" 选项
      setSelectedSubjectsState(['全部']);
    }
  }, [selectedSubjectsState]);

  useEffect(() => {
    setChartModalOptions(chartOptions);
  }, [chartOptions]);

  const columns = [
    { title: '班级', dataIndex: 'className', key: 'className' },
    { title: '班主任', dataIndex: 'teacherName', key: 'teacherName' },
    { title: '应考人数', dataIndex: 'classStudentNum', key: 'classStudentNum' },
    { title: '实考人数', dataIndex: 'submitStudentNum', key: 'submitStudentNum' },
    { title: '满分', dataIndex: 'standardScore', key: 'standardScore' },
    { title: '最高分', dataIndex: 'maxScore', key: 'maxScore' },
    { title: '最低分', dataIndex: 'minScore', key: 'minScore' },
    { title: '平均分', dataIndex: 'avgScore', key: 'avgScore' },
    { title: '名次', dataIndex: 'rank', key: 'rank' },
    { title: '优秀率', dataIndex: 'excellentRate', key: 'excellentRate' },
    { title: '合格率', dataIndex: 'qualifiedRate', key: 'qualifiedRate' },
    { title: '低分率', dataIndex: 'lowScoreRate', key: 'lowScoreRate' },
    { title: '超均率', dataIndex: 'overAverage', key: 'overAverage' }
  ];

  const getTableCanvas = async (targetData?: GetSubjectSummarizationTableType[], targetGradeName?: string) => {
    const tableData = targetData || data;
    const gradeName = targetGradeName || selectedGradesName;

    if (!tableRef.current || !tableData.length) return '';

    try {
      // 设置为加载状态
      setIsLoading(true);

      // 使用html2canvas将表格转换为canvas
      const canvas = await html2canvas(tableRef.current, {
        scale: 2, // 提高清晰度
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true
      });

      // 将canvas转换为blob数据
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          if (blob) resolve(blob);
        }, 'image/png', 1.0);
      });

      // 创建文件对象
      const file = new File([blob], `${gradeName}各班数据汇总统计表.png`, { type: 'image/png' });

      // 上传文件到服务器
      const formData = new FormData();
      formData.append('file', file);

      const response = await uploadFile(formData);

      if (response && response.fileUrl) {
        setTableImageUrl(response.fileUrl);
        // 如果父组件提供了回调函数，则将图片URL传递给父组件
        if (onImageUrlChange) {
          console.log(response.fileUrl, 'response.fileUrl');
          onImageUrlChange(response.fileUrl);
        }
        return response.fileUrl;
      }
      return '';
    } catch (error) {
      console.error('生成表格图片失败:', error);
      return '';
    } finally {
      setIsLoading(false);
    }
  };

  // 独立的数据获取函数，用于导出时使用
  const fetchDataForExport = async (params: {
    year: string;
    term: number;
    startDate: string;
    endDate: string;
    selectedSubjects: string[];
    selectedGradesName: string;
    selectedGradesId: string;
    selectedTestData: string[];
  }) => {
    const requestParams = {
      year: params.year,
      term: params.term,
      startDate: params.startDate,
      endDate: params.endDate,
      subjectNames: params.selectedSubjects,
      gradeName: params.selectedGradesName,
      gradeId: params.selectedGradesId,
      examType: params.selectedTestData?.[0] || '',
      teacherType: teacherType,
      examId: params.selectedTestData?.[1] || ''
    };

    try {
      const response = await getSubjectSummarizationTable(requestParams);
      const examSummaryData = generateExamSummary(requestParams, response);
      return { tableData: response, examSummary: examSummaryData };
    } catch (error) {
      console.error('Error fetching data for export:', error);
      throw error;
    }
  };

  // 创建临时表格DOM用于生成图片
  const createTempTableForImage = (tableData: GetSubjectSummarizationTableType[], gradeName: string, subjects: string[]) => {
    // 创建临时容器
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '-9999px';
    tempContainer.style.width = '1200px';
    tempContainer.style.backgroundColor = '#ffffff';
    tempContainer.style.padding = '20px';

    // 创建标题
    const title = document.createElement('h2');
    title.textContent = `${gradeName}${subjects.join('、')}各班数据汇总统计表`;
    title.style.textAlign = 'center';
    title.style.marginBottom = '16px';
    title.style.fontWeight = '700';
    title.style.fontSize = '24px';
    title.style.color = '#000';

    // 创建表格
    const table = document.createElement('table');
    table.style.width = '100%';
    table.style.borderCollapse = 'collapse';
    table.style.border = '1px solid #d9d9d9';
    table.style.fontSize = '14px';

    // 创建表头
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    headerRow.style.backgroundColor = '#fafafa';

    const headers = ['班级', '班主任', '应考人数', '实考人数', '满分', '最高分', '最低分', '平均分', '名次', '优秀率', '合格率', '低分率', '超均率'];
    headers.forEach(header => {
      const th = document.createElement('th');
      th.textContent = header;
      th.style.padding = '12px 8px';
      th.style.border = '1px solid #d9d9d9';
      th.style.textAlign = 'center';
      th.style.fontWeight = '600';
      headerRow.appendChild(th);
    });
    thead.appendChild(headerRow);
    table.appendChild(thead);

    // 创建表体
    const tbody = document.createElement('tbody');
    tableData.forEach(row => {
      const tr = document.createElement('tr');
      const fields = ['className', 'teacherName', 'classStudentNum', 'submitStudentNum', 'standardScore', 'maxScore', 'minScore', 'avgScore', 'rank', 'excellentRate', 'qualifiedRate', 'lowScoreRate', 'overAverage'];
      fields.forEach(field => {
        const td = document.createElement('td');
        td.textContent = (row as any)[field] || '';
        td.style.padding = '12px 8px';
        td.style.border = '1px solid #d9d9d9';
        td.style.textAlign = 'center';
        tr.appendChild(td);
      });
      tbody.appendChild(tr);
    });
    table.appendChild(tbody);

    tempContainer.appendChild(title);
    tempContainer.appendChild(table);
    document.body.appendChild(tempContainer);

    return tempContainer;
  };

  // 当表格数据更新时生成图片
  useEffect(() => {
    if (viewMode === 'table' && data.length > 0 && tableRef.current) {
      // 等DOM更新后再生成图片
      const timer = setTimeout(() => {
        getTableCanvas();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [data, viewMode]);

  // 暴露方法和属性给父组件
  useImperativeHandle(ref, () => ({
    getTableImageUrl: () => tableImageUrl,
    generateTableImage: async (params) => {
      if (params) {
        // 使用传入的参数获取数据并生成图片
        try {
          const { tableData } = await fetchDataForExport(params);

          // 创建临时表格DOM
          const tempContainer = createTempTableForImage(tableData, params.selectedGradesName, params.selectedSubjects);

          try {
            // 使用html2canvas将临时表格转换为canvas
            const canvas = await html2canvas(tempContainer, {
              scale: 2,
              backgroundColor: '#ffffff',
              logging: false,
              useCORS: true
            });

            // 将canvas转换为blob数据
            const blob = await new Promise<Blob>((resolve) => {
              canvas.toBlob((blob) => {
                if (blob) resolve(blob);
              }, 'image/png', 1.0);
            });

            // 创建文件对象
            const file = new File([blob], `${params.selectedGradesName}各班数据汇总统计表.png`, { type: 'image/png' });

            // 上传文件到服务器
            const formData = new FormData();
            formData.append('file', file);

            const response = await uploadFile(formData);

            if (response && response.fileUrl) {
              setTableImageUrl(response.fileUrl);
              if (onImageUrlChange) {
                onImageUrlChange(response.fileUrl);
              }
              return response.fileUrl;
            }
            return '';
          } finally {
            // 清理临时DOM
            document.body.removeChild(tempContainer);
          }
        } catch (error) {
          console.error('生成表格图片失败:', error);
          return '';
        }
      } else {
        // 使用当前数据生成图片
        if (viewMode === 'table' && tableRef.current) {
          return await getTableCanvas();
        }
        return '';
      }
    },
    getBoardPaneContent: async (params) => {
      if (params) {
        // 使用传入的参数获取数据
        try {
          const { examSummary } = await fetchDataForExport(params);
          // 这里需要等待BoardPane组件处理完数据后返回内容
          // 由于BoardPane是异步的，我们需要模拟其行为或者返回examSummary数据
          return JSON.stringify(examSummary);
        } catch (error) {
          console.error('获取BoardPane内容失败:', error);
          return '';
        }
      } else {
        return boardPaneContent;
      }
    },
    refreshBoardPane: () => {
      boardPaneRef.current?.refresh();
    }
  }), [tableImageUrl, viewMode, boardPaneContent, selectedSubjects, teacherType]);

  // 监听BoardPane的内容变化
  const handleBoardPaneContentUpdate = (content: string) => {
    setBoardPaneContent(content);
  };

  return (
    <MyModal
      isOpen={isModalVisible}
      onClose={handleCancel}
      isCentered
      minW={chartHeight}
      h="800px"
      headerStyle={{
        background: '#fff'
      }}
      title={
        <Box
          style={{
            width: '100%',
            height: '50px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
        >
          <span>查看数据汇总</span>
          <Box display="flex" alignItems="center">
            <Flex borderRadius="50px" p="6px" mr="16px" justifyContent="center">
              <Segmented
                value={viewMode}
                onChange={(value) => {
                  setViewMode(value as 'chart' | 'table');
                }}
                options={[
                  { value: 'chart', label: '柱状图' },
                  { value: 'table', label: '表格' }
                ]}
                className={styles.customSegmented}
              />
            </Flex>

            <Select
              options={subjectOptions}
              style={{ width: '200px', marginRight: '16px' }}
              value={selectedSubjectsState}
              onChange={(value) => {
                setSelectedSubjectsState(value);
              }}
              placeholder="请选择科目"
              dropdownStyle={{ zIndex: 9999 }}
              mode="multiple"
              maxTagCount={3}
            />
            <Button onClick={handleExport}>导出</Button>
            {viewMode === 'table' && tableImageUrl && (
              <>
                <Button
                  onClick={() => console.log('表格图片URL:', tableImageUrl)}
                  style={{ marginLeft: '8px' }}
                >
                  打印图片URL
                </Button>
                <Button
                  onClick={handleCopyUrl}
                  style={{ marginLeft: '8px' }}
                >
                  复制图片URL
                </Button>
              </>
            )}
          </Box>
        </Box>
      }
    >
      <ModalBody mt="16px">
        {viewMode === 'table' && (
          <div ref={tableRef}>
            <h2
              style={{ textAlign: 'center', marginBottom: '16px', fontWeight: 700, fontSize: '24px' }}
            >
              {selectedGradesName}
              {selectedSubjectsState.length > 0 ? selectedSubjectsState.join('、') : selectedSubjects.join('、')}各班数据汇总统计表
            </h2>
            <Table
              columns={columns}
              dataSource={data}
              pagination={false}
              style={{ marginTop: '16px', marginBottom: '16px' }}
              rowKey="className"
            />
          </div>
        )}
        {viewMode === 'table' ? (
          <Box>
            <BoardPane
              ref={boardPaneRef}
              location={1}
              height="236px"
              examId={selectedTestData?.[1] || ''}
              selectedSubjects={selectedSubjects}
              selectedTestData={selectedTestData}
              selectedGradesName={selectedGradesName}
              selectedGradesId={selectedGradesId}
              editable={true}
              params={examSummary}
              fullScreenHeight="auto"
              fullScreenoffsetX="40"
              title="结论"
              backgroundColor="#f9f9f9"
              titleFontSize="14px"
              padding="10px"
              showFullScreenButtons
              onContentUpdate={handleBoardPaneContentUpdate}
            />
          </Box>
        ) : (
          <Box>
            <Box color="#1d2129" fontSize="16px" fontWeight="500" pl="28px">
              数据汇总
            </Box>

            {isLoading && (
              <Box
                position="absolute"
                top="0"
                left="0"
                right="0"
                bottom="0"
                bg="rgba(255, 255, 255, 255)"
                display="flex"
                alignItems="center"
                justifyContent="center"
                zIndex="1"
              >
                <Lottie
                  name="chating"
                  w={respDims(`${(130 / 3) * 2}rpx`, 130 / 3)}
                  h={respDims(`${(45 / 3) * 2}rpx`, 45 / 3)}
                />
              </Box>
            )}

            {Object.keys(chartModalOptions).length > 0 ? (
              <EChartsReact
                ref={echartsRef}
                option={chartModalOptions}
                style={{ borderRadius: '12px', height: '480px', position: 'relative' }}
              />
            ) : (
              <NoDataComponent type={isAuthority ? 'noPermission' : 'noData'} />
            )}

            {Object.keys(chartModalOptions).length > 0 ? (
              <BoardPane
                ref={boardPaneRef}
                location={1}
                height="236px"
                examId={selectedTestData?.[1] || ''}
                selectedSubjects={selectedSubjects}
                selectedTestData={selectedTestData}
                selectedGradesName={selectedGradesName}
                selectedGradesId={selectedGradesId}
                editable={true}
                params={examSummary}
                fullScreenHeight="auto"
                fullScreenoffsetX="40"
                title="结论"
                backgroundColor="#f9f9f9"
                titleFontSize="14px"
                padding="10px"
                showFullScreenButtons
                onContentUpdate={handleBoardPaneContentUpdate}
              />
            ) : (
              ''
            )}
          </Box>
        )}
      </ModalBody>
    </MyModal>
  );
});

DataSummaryModal.displayName = 'DataSummaryModal';

export default DataSummaryModal;
